package sirobilt.meghasanjivini.appointment.dto

data class ApiResponse<T>(
    val success: Boolean = true,
    val data: T
)

data class Pagination(
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Int
)

data class PaginatedResponse<T>(
    val success: Boolean = true,
    val data: T,
    val pagination: Pagination
)

data class AppointmentSearchRequest(
    val patientId: String? = null,
    val providerId: String? = null,
    val facilityId: String? = null,
    val status: List<String>? = null,
    val type: List<String>? = null,
    val priority: List<String>? = null,
    val dateFrom: String? = null, // "YYYY-MM-DD"
    val dateTo: String? = null,
    val searchTerm: String? = null,
    val page: Int? = 0,
    val size: Int? = 20
)

data class SlotOperationResult(
    val slotId: Int,
    val action: String, // "freed", "not_freed", "not_found"
    val reason: String,
    val slotDate: String? = null,
    val slotTime: String? = null,
    val minutesRemaining: Long? = null
)

data class AppointmentOperationResponse(
    val appointmentId: String,
    val operation: String, // "delete", "cancel", "update", "reschedule"
    val success: Boolean,
    val message: String,
    val slotOperation: SlotOperationResult? = null
)

data class AppointmentWithSlotOperationResponse(
    val appointment: AppointmentResponseDTO,
    val slotOperation: SlotOperationResult? = null,
    val message: String? = null
)